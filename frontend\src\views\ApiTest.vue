<template>
  <div class="api-test-page">
    <el-card>
      <template #header>
        <h2>API 连接测试</h2>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <el-button type="primary" @click="testConnectionAPI" :loading="loading">
          测试连接
        </el-button>

        <el-button type="success" @click="testHealthCheckAPI" :loading="healthLoading">
          健康检查
        </el-button>

        <el-button type="info" @click="testDishesAPICall" :loading="dishLoading">
          测试菜品API
        </el-button>
        
        <el-divider>测试结果</el-divider>
        
        <el-card v-if="result" :class="result.success ? 'success-card' : 'error-card'">
          <h4>{{ result.title }}</h4>
          <p><strong>状态:</strong> {{ result.success ? '成功' : '失败' }}</p>
          <p><strong>响应:</strong> {{ result.message }}</p>
          <pre v-if="result.data">{{ JSON.stringify(result.data, null, 2) }}</pre>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { testConnection, healthCheck } from '@/api/test'
import { getDishes } from '@/api/dish'

const loading = ref(false)
const healthLoading = ref(false)
const dishLoading = ref(false)
const result = ref(null)

const testConnectionAPI = async () => {
  loading.value = true
  try {
    const response = await testConnection()
    result.value = {
      title: '连接测试',
      success: true,
      message: response.data || response.message,
      data: response
    }
    ElMessage.success('连接测试成功')
  } catch (error) {
    result.value = {
      title: '连接测试',
      success: false,
      message: error.message || '连接失败',
      data: error
    }
    ElMessage.error('连接测试失败')
  } finally {
    loading.value = false
  }
}

const testHealthCheckAPI = async () => {
  healthLoading.value = true
  try {
    const response = await healthCheck()
    result.value = {
      title: '健康检查',
      success: true,
      message: response.data || response.message,
      data: response
    }
    ElMessage.success('健康检查成功')
  } catch (error) {
    result.value = {
      title: '健康检查',
      success: false,
      message: error.message || '健康检查失败',
      data: error
    }
    ElMessage.error('健康检查失败')
  } finally {
    healthLoading.value = false
  }
}

const testDishesAPICall = async () => {
  dishLoading.value = true
  try {
    const response = await getDishes({ page: 1, size: 5 })
    result.value = {
      title: '菜品API测试',
      success: true,
      message: '菜品API调用成功',
      data: response
    }
    ElMessage.success('菜品API测试成功')
  } catch (error) {
    result.value = {
      title: '菜品API测试',
      success: false,
      message: error.message || '菜品API调用失败',
      data: error
    }
    ElMessage.error('菜品API测试失败')
  } finally {
    dishLoading.value = false
  }
}


</script>

<style scoped>
.api-test-page {
  padding: 20px;
}

.success-card {
  border-color: #67c23a;
}

.error-card {
  border-color: #f56c6c;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
