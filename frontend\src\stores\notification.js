import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getNotifications, markAsRead, markAllAsRead } from '@/api/notification'

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref([])
  const loading = ref(false)
  const unreadCount = ref(0)

  // 计算属性
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read)
  )

  const recentNotifications = computed(() => 
    notifications.value.slice(0, 10)
  )

  // 获取通知列表
  const fetchNotifications = async () => {
    loading.value = true
    try {
      const response = await getNotifications()
      notifications.value = response.data.list || []
      unreadCount.value = unreadNotifications.value.length
      return response
    } catch (error) {
      console.error('获取通知失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 标记单个通知为已读
  const markNotificationAsRead = async (id) => {
    try {
      await markAsRead(id)
      const notification = notifications.value.find(n => n.id === id)
      if (notification) {
        notification.read = true
        unreadCount.value = unreadNotifications.value.length
      }
    } catch (error) {
      console.error('标记通知已读失败:', error)
      throw error
    }
  }

  // 标记所有通知为已读
  const markAllNotificationsAsRead = async () => {
    try {
      await markAllAsRead()
      notifications.value.forEach(n => n.read = true)
      unreadCount.value = 0
    } catch (error) {
      console.error('标记所有通知已读失败:', error)
      throw error
    }
  }

  // 添加新通知（用于实时通知）
  const addNotification = (notification) => {
    notifications.value.unshift(notification)
    unreadCount.value = unreadNotifications.value.length
  }

  // 清空通知
  const clearNotifications = () => {
    notifications.value = []
    unreadCount.value = 0
  }

  return {
    // 状态
    notifications,
    loading,
    unreadCount,
    
    // 计算属性
    unreadNotifications,
    recentNotifications,
    
    // 方法
    fetchNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    addNotification,
    clearNotifications
  }
})
