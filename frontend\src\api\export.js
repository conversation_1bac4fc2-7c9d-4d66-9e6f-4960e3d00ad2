import request from './request'

// 导出订单报表
export const exportOrderReport = (params = {}) => {
  return request({
    url: '/api/export/orders',
    method: 'get',
    params,
    responseType: 'blob' // 重要：设置响应类型为blob
  })
}

// 导出用户报表
export const exportUserReport = (params = {}) => {
  return request({
    url: '/api/export/users',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出菜品报表
export const exportDishReport = (params = {}) => {
  return request({
    url: '/api/export/dishes',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出销售报表
export const exportSalesReport = (params = {}) => {
  return request({
    url: '/api/export/sales',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 通用文件下载函数
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
