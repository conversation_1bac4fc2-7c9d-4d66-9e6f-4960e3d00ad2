import request from './request'

// 获取通知列表
export const getNotifications = (params = {}) => {
  return request({
    url: '/api/notifications',
    method: 'get',
    params
  })
}

// 标记通知为已读
export const markAsRead = (id) => {
  return request({
    url: `/api/notifications/${id}/read`,
    method: 'put'
  })
}

// 标记所有通知为已读
export const markAllAsRead = () => {
  return request({
    url: '/api/notifications/read-all',
    method: 'put'
  })
}

// 删除通知
export const deleteNotification = (id) => {
  return request({
    url: `/api/notifications/${id}`,
    method: 'delete'
  })
}

// 获取未读通知数量
export const getUnreadCount = () => {
  return request({
    url: '/api/notifications/unread-count',
    method: 'get'
  })
}
