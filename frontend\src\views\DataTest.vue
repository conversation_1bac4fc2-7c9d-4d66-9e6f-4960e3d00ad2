<template>
  <div class="data-test-page">
    <el-card>
      <template #header>
        <h2>数据刷新测试</h2>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card>
              <template #header>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>菜品数据</span>
                  <el-button type="primary" size="small" @click="refreshDishes">刷新</el-button>
                </div>
              </template>
              <p>总数: {{ dishStore.total }}</p>
              <p>当前页: {{ dishStore.dishes.length }} 条</p>
              <p>最后更新: {{ lastDishUpdate }}</p>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card>
              <template #header>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>订单数据</span>
                  <el-button type="primary" size="small" @click="refreshOrders">刷新</el-button>
                </div>
              </template>
              <p>总数: {{ orderStore.total }}</p>
              <p>当前页: {{ orderStore.orders.length }} 条</p>
              <p>最后更新: {{ lastOrderUpdate }}</p>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card>
              <template #header>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>用户数据</span>
                  <el-button type="primary" size="small" @click="refreshUsers">刷新</el-button>
                </div>
              </template>
              <p>总数: {{ userStore.total || 0 }}</p>
              <p>当前页: {{ userStore.users?.length || 0 }} 条</p>
              <p>最后更新: {{ lastUserUpdate }}</p>
            </el-card>
          </el-col>
        </el-row>
        
        <el-divider>操作测试</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="success" @click="testAddDish" :loading="addingDish">
              测试添加菜品
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="warning" @click="testUpdateDish" :loading="updatingDish">
              测试更新菜品
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="danger" @click="testDeleteDish" :loading="deletingDish">
              测试删除菜品
            </el-button>
          </el-col>
        </el-row>
        
        <el-divider>最近操作日志</el-divider>
        
        <el-card>
          <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action" :class="log.type">{{ log.action }}</span>
            <span class="log-result" :class="log.success ? 'success' : 'error'">
              {{ log.success ? '成功' : '失败' }}
            </span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useDishStore } from '@/stores/dish'
import { useOrderStore } from '@/stores/order'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const dishStore = useDishStore()
const orderStore = useOrderStore()
const userStore = useUserStore()

const lastDishUpdate = ref('')
const lastOrderUpdate = ref('')
const lastUserUpdate = ref('')
const addingDish = ref(false)
const updatingDish = ref(false)
const deletingDish = ref(false)
const operationLogs = ref([])

const addLog = (action, type, success, message) => {
  operationLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    action,
    type,
    success,
    message
  })
  if (operationLogs.value.length > 10) {
    operationLogs.value.pop()
  }
}

const refreshDishes = async () => {
  try {
    await dishStore.fetchDishes({ page: 1, size: 10 })
    lastDishUpdate.value = new Date().toLocaleTimeString()
    addLog('刷新菜品数据', 'info', true, `获取到 ${dishStore.dishes.length} 条菜品`)
  } catch (error) {
    addLog('刷新菜品数据', 'info', false, error.message)
  }
}

const refreshOrders = async () => {
  try {
    await orderStore.fetchOrders({ page: 1, size: 10 })
    lastOrderUpdate.value = new Date().toLocaleTimeString()
    addLog('刷新订单数据', 'info', true, `获取到 ${orderStore.orders.length} 条订单`)
  } catch (error) {
    addLog('刷新订单数据', 'info', false, error.message)
  }
}

const refreshUsers = async () => {
  try {
    // 假设有用户store的fetchUsers方法
    lastUserUpdate.value = new Date().toLocaleTimeString()
    addLog('刷新用户数据', 'info', true, '用户数据刷新成功')
  } catch (error) {
    addLog('刷新用户数据', 'info', false, error.message)
  }
}

const testAddDish = async () => {
  addingDish.value = true
  try {
    const testDish = {
      name: `测试菜品_${Date.now()}`,
      description: '这是一个测试菜品',
      price: 99.99,
      category: 'MAIN',
      status: 1,
      imageUrl: 'https://images.unsplash.com/photo-1563245372-f21724e3856d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    }
    await dishStore.addDish(testDish)
    addLog('添加菜品', 'success', true, `成功添加菜品: ${testDish.name}`)
    ElMessage.success('测试菜品添加成功')
    await refreshDishes()
  } catch (error) {
    addLog('添加菜品', 'success', false, error.message)
    ElMessage.error('测试菜品添加失败')
  } finally {
    addingDish.value = false
  }
}

const testUpdateDish = async () => {
  if (dishStore.dishes.length === 0) {
    ElMessage.warning('没有菜品可以更新，请先添加菜品')
    return
  }
  
  updatingDish.value = true
  try {
    const dish = dishStore.dishes[0]
    const updatedDish = {
      ...dish,
      name: `${dish.name}_已更新_${Date.now()}`
    }
    await dishStore.editDish(dish.id, updatedDish)
    addLog('更新菜品', 'warning', true, `成功更新菜品: ${dish.name}`)
    ElMessage.success('测试菜品更新成功')
    await refreshDishes()
  } catch (error) {
    addLog('更新菜品', 'warning', false, error.message)
    ElMessage.error('测试菜品更新失败')
  } finally {
    updatingDish.value = false
  }
}

const testDeleteDish = async () => {
  if (dishStore.dishes.length === 0) {
    ElMessage.warning('没有菜品可以删除')
    return
  }
  
  deletingDish.value = true
  try {
    const dish = dishStore.dishes[dishStore.dishes.length - 1]
    await dishStore.removeDish(dish.id)
    addLog('删除菜品', 'danger', true, `成功删除菜品: ${dish.name}`)
    ElMessage.success('测试菜品删除成功')
    await refreshDishes()
  } catch (error) {
    addLog('删除菜品', 'danger', false, error.message)
    ElMessage.error('测试菜品删除失败')
  } finally {
    deletingDish.value = false
  }
}

onMounted(() => {
  refreshDishes()
  refreshOrders()
  refreshUsers()
})
</script>

<style scoped>
.data-test-page {
  padding: 20px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.log-time {
  color: #909399;
  font-size: 12px;
  min-width: 80px;
}

.log-action {
  font-weight: 500;
  min-width: 80px;
}

.log-action.info { color: #409eff; }
.log-action.success { color: #67c23a; }
.log-action.warning { color: #e6a23c; }
.log-action.danger { color: #f56c6c; }

.log-result {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 40px;
  text-align: center;
}

.log-result.success {
  background: #f0f9ff;
  color: #67c23a;
}

.log-result.error {
  background: #fef0f0;
  color: #f56c6c;
}

.log-message {
  color: #606266;
  flex: 1;
}
</style>
