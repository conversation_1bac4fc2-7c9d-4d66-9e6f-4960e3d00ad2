<template>
  <div class="create-order-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>创建订单</h2>
        <p>为客户创建新的订单</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 菜品选择区域 -->
      <el-col :xs="24" :sm="24" :md="14" :lg="16">
        <el-card class="dishes-card">
          <template #header>
            <div class="card-header">
              <span>选择菜品</span>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索菜品"
                style="width: 200px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </template>

          <!-- 分类标签 -->
          <div class="category-tabs">
            <el-tag
              v-for="category in categories"
              :key="category.value"
              :type="selectedCategory === category.value ? 'primary' : 'info'"
              :effect="selectedCategory === category.value ? 'dark' : 'plain'"
              @click="selectedCategory = category.value"
              class="category-tag"
            >
              {{ category.label }}
            </el-tag>
          </div>

          <!-- 菜品网格 -->
          <div v-loading="dishesLoading" class="dishes-grid">
            <div
              v-for="dish in filteredDishes"
              :key="dish.id"
              class="dish-card"
              @click="addDishToOrder(dish)"
            >
              <div class="dish-image">
                <img :src="dish.imageUrl || '/placeholder-dish.jpg'" :alt="dish.name" />
              </div>
              <div class="dish-info">
                <h4 class="dish-name">{{ dish.name }}</h4>
                <p class="dish-price">¥{{ dish.price }}</p>
              </div>
            </div>

            <!-- 暂无菜品 -->
            <div v-if="!dishesLoading && filteredDishes.length === 0" class="empty-dishes">
              <el-icon size="48" color="#c0c4cc"><Dish /></el-icon>
              <p>暂无菜品</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 订单详情区域 -->
      <el-col :xs="24" :sm="24" :md="10" :lg="8">
        <el-card class="order-card" :body-style="{ padding: '0' }">
          <template #header>
            <div class="card-header">
              <span>订单详情</span>
              <el-button type="text" @click="clearOrder" :disabled="orderItems.length === 0">
                清空
              </el-button>
            </div>
          </template>

          <!-- 基本信息 -->
          <div class="order-info">
            <el-form :model="orderForm" label-width="80px">
              <el-form-item label="桌号" required>
                <el-input v-model="orderForm.tableNumber" placeholder="请输入桌号" />
              </el-form-item>
            </el-form>
          </div>

          <!-- 订单项列表 -->
          <div class="order-items">
            <div v-if="orderItems.length === 0" class="empty-order">
              <el-icon size="48" color="#c0c4cc"><ShoppingCart /></el-icon>
              <p>请选择菜品</p>
            </div>
            
            <div v-else class="items-list">
              <div
                v-for="item in orderItems"
                :key="item.dishId"
                class="order-item"
              >
                <div class="item-info">
                  <h5 class="item-name">{{ item.dishName }}</h5>
                  <p class="item-price">¥{{ item.price }}</p>
                </div>
                <div class="item-controls">
                  <el-input-number
                    v-model="item.quantity"
                    :min="1"
                    :max="99"
                    size="small"
                    @change="updateItemTotal(item)"
                  />
                  <el-button
                    type="text"
                    @click="removeItem(item.dishId)"
                    class="remove-btn"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单总计 -->
          <div class="order-summary">
            <div class="summary-row">
              <span>商品总数：</span>
              <span>{{ totalQuantity }} 份</span>
            </div>
            <div class="summary-row total">
              <span>总金额：</span>
              <span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
            </div>
          </div>

          <!-- 提交按钮 -->
          <div class="order-actions">
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              :disabled="orderItems.length === 0 || !orderForm.tableNumber"
              @click="submitOrder"
              style="width: 100%"
            >
              <el-icon><Check /></el-icon>
              提交订单
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore } from '@/stores/order'
import { useDishStore } from '@/stores/dish'
import { ElMessage } from 'element-plus'

const router = useRouter()
const orderStore = useOrderStore()
const dishStore = useDishStore()

const loading = ref(false)
const dishesLoading = computed(() => dishStore.loading)
const searchKeyword = ref('')
const selectedCategory = ref('ALL')

const orderForm = reactive({
  tableNumber: ''
})

const orderItems = ref([])
const dishes = computed(() => dishStore.dishes)

const categories = [
  { label: '全部', value: 'ALL' },
  { label: '开胃菜', value: 'APPETIZER' },
  { label: '主菜', value: 'MAIN' },
  { label: '甜点', value: 'DESSERT' },
  { label: '饮品', value: 'DRINK' }
]



const filteredDishes = computed(() => {
  if (!dishes.value || !Array.isArray(dishes.value)) {
    return []
  }

  let filtered = dishes.value.filter(dish => dish.status === 1) // 只显示可用的菜品

  // 按分类筛选
  if (selectedCategory.value !== 'ALL') {
    filtered = filtered.filter(dish => dish.category === selectedCategory.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    filtered = filtered.filter(dish =>
      dish.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return filtered
})

const totalQuantity = computed(() => {
  return orderItems.value.reduce((total, item) => total + item.quantity, 0)
})

const totalPrice = computed(() => {
  return orderItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
})

const addDishToOrder = (dish) => {
  const existingItem = orderItems.value.find(item => item.dishId === dish.id)
  
  if (existingItem) {
    existingItem.quantity += 1
  } else {
    orderItems.value.push({
      dishId: dish.id,
      dishName: dish.name,
      price: dish.price,
      quantity: 1
    })
  }
}

const updateItemTotal = (item) => {
  // 数量变化时的处理逻辑
}

const removeItem = (dishId) => {
  const index = orderItems.value.findIndex(item => item.dishId === dishId)
  if (index > -1) {
    orderItems.value.splice(index, 1)
  }
}

const clearOrder = () => {
  orderItems.value = []
}

const submitOrder = async () => {
  if (!orderForm.tableNumber) {
    ElMessage.error('请输入桌号')
    return
  }

  if (orderItems.value.length === 0) {
    ElMessage.error('请选择菜品')
    return
  }

  loading.value = true
  try {
    const orderData = {
      tableNumber: orderForm.tableNumber,
      items: orderItems.value.map(item => ({
        dishId: item.dishId,
        quantity: item.quantity,
        price: item.price,
        notes: ''
      })),
      totalPrice: totalPrice.value
    }

    await orderStore.addOrder(orderData)
    ElMessage.success('订单创建成功')
    router.push('/orders')
  } catch (error) {
    ElMessage.error(error.message || '创建订单失败')
  } finally {
    loading.value = false
  }
}

// 获取菜品数据
const fetchDishes = async () => {
  try {
    await dishStore.fetchAvailableDishes()
  } catch (error) {
    ElMessage.error('获取菜品列表失败')
  }
}

onMounted(() => {
  fetchDishes()
})
</script>

<style scoped>
.create-order-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.dishes-card,
.order-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: calc(100vh - 200px);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.category-tabs {
  margin-bottom: 20px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.dishes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  max-height: calc(100vh - 350px);
  overflow-y: auto;
}

.dish-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.dish-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.dish-image {
  height: 100px;
  overflow: hidden;
}

.dish-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dish-info {
  padding: 12px;
  text-align: center;
}

.dish-name {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.dish-price {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}

.order-card {
  display: flex;
  flex-direction: column;
}

.order-info {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.order-items {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 450px);
}

.empty-order {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-order p {
  margin: 12px 0 0 0;
  font-size: 14px;
}

.empty-dishes {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-dishes p {
  margin: 12px 0 0 0;
  font-size: 14px;
}

.items-list {
  padding: 0 20px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f5f7fa;
}

.item-info {
  flex: 1;
}

.item-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.item-price {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.remove-btn {
  color: #f56c6c;
  padding: 4px;
}

.order-summary {
  padding: 20px;
  border-top: 1px solid #ebeef5;
  background: #fafafa;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-row.total {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 0;
}

.total-price {
  color: #409eff;
}

.order-actions {
  padding: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .dishes-card,
  .order-card {
    height: auto;
    margin-bottom: 20px;
  }
  
  .dishes-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    max-height: 400px;
  }
  
  .order-items {
    max-height: 300px;
  }
}
</style>
