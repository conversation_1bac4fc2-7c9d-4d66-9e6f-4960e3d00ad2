<template>
  <div class="edit-dish-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>编辑菜品</h2>
        <p>修改菜品信息</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <el-card v-loading="pageLoading" class="form-card">
      <el-form
        ref="dishFormRef"
        :model="dishForm"
        :rules="dishRules"
        label-width="100px"
        class="dish-form"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12">
            <el-form-item label="菜品名称" prop="name">
              <el-input
                v-model="dishForm.name"
                placeholder="请输入菜品名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="菜品分类" prop="category">
              <el-select v-model="dishForm.category" placeholder="请选择菜品分类" style="width: 100%">
                <el-option label="开胃菜" value="APPETIZER" />
                <el-option label="主菜" value="MAIN" />
                <el-option label="甜点" value="DESSERT" />
                <el-option label="饮品" value="DRINK" />
              </el-select>
            </el-form-item>

            <el-form-item label="菜品价格" prop="price">
              <el-input-number
                v-model="dishForm.price"
                :min="0"
                :max="9999.99"
                :precision="2"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="菜品状态" prop="status">
              <el-radio-group v-model="dishForm.status">
                <el-radio :label="1">上架中</el-radio>
                <el-radio :label="0">已下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="24" :md="12" :lg="12">
            <el-form-item label="菜品图片" prop="imageUrl">
              <el-upload
                class="image-uploader"
                :action="uploadAction"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleImageSuccess"
                :on-error="handleImageError"
                :before-upload="beforeImageUpload"
              >
                <img v-if="dishForm.imageUrl" :src="dishForm.imageUrl" class="uploaded-image" />
                <div v-else class="upload-placeholder">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <div class="upload-text">点击上传图片</div>
                </div>
              </el-upload>
              <div class="upload-tip">
                只能上传jpg/png文件，且不超过2MB
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="菜品描述" prop="description">
          <el-input
            v-model="dishForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入菜品描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            <el-icon><Check /></el-icon>
            保存修改
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button @click="$router.back()">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDishStore } from '@/stores/dish'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const dishStore = useDishStore()
const userStore = useUserStore()

const dishFormRef = ref()
const loading = ref(false)
const pageLoading = ref(false)
const originalForm = ref({})

const dishForm = reactive({
  name: '',
  description: '',
  price: 0,
  category: '',
  status: 1,
  imageUrl: ''
})

const dishRules = {
  name: [
    { required: true, message: '请输入菜品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '菜品名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择菜品分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入菜品价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

const uploadAction = computed(() => '/api/dishes/upload')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))

const fetchDishData = async () => {
  const dishId = route.params.id
  if (!dishId) return

  pageLoading.value = true
  try {
    await dishStore.fetchDish(dishId)
    const dish = dishStore.currentDish

    if (dish) {
      Object.assign(dishForm, dish)
      originalForm.value = { ...dish }
    }
  } catch (error) {
    ElMessage.error('获取菜品信息失败')
    router.back()
  } finally {
    pageLoading.value = false
  }
}

const handleImageSuccess = (response) => {
  if (response.code === 200) {
    dishForm.imageUrl = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleImageError = () => {
  ElMessage.error('图片上传失败')
}

const beforeImageUpload = (file) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPGOrPNG) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleSubmit = async () => {
  if (!dishFormRef.value) return

  try {
    await dishFormRef.value.validate()
    loading.value = true

    const dishId = route.params.id
    await dishStore.editDish(dishId, dishForm)
    ElMessage.success('菜品修改成功')
    router.push('/dishes')
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  Object.assign(dishForm, originalForm.value)
  dishFormRef.value?.clearValidate()
}

onMounted(() => {
  fetchDishData()
})
</script>

<style scoped>
.edit-dish-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.form-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dish-form {
  max-width: 800px;
}

.image-uploader {
  width: 100%;
}

.image-uploader :deep(.el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader :deep(.el-upload:hover) {
  border-color: #409eff;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .dish-form {
    max-width: 100%;
  }
  
  .dish-form .el-col {
    margin-bottom: 0;
  }
}
</style>
