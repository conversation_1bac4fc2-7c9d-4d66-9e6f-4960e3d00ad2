com\hotel\entity\Order.class
com\hotel\common\Result.class
com\hotel\controller\AuthController.class
com\hotel\HotelOrderSystemApplication.class
com\hotel\security\JwtAuthenticationEntryPoint.class
com\hotel\service\AuthService.class
com\hotel\dto\CreateOrderRequest.class
com\hotel\entity\User.class
com\hotel\dto\CreateOrderRequest$OrderItemRequest.class
com\hotel\config\MybatisPlusConfig.class
com\hotel\entity\Dish.class
com\hotel\mapper\UserMapper.class
com\hotel\exception\GlobalExceptionHandler.class
com\hotel\mapper\OrderMapper.class
com\hotel\service\DishService.class
com\hotel\dto\LoginResponse.class
com\hotel\common\PageResult.class
com\hotel\service\impl\DishServiceImpl.class
com\hotel\exception\BusinessException.class
com\hotel\utils\JwtUtils.class
com\hotel\mapper\DishMapper.class
com\hotel\controller\DishController.class
com\hotel\service\OrderService.class
com\hotel\controller\UserController.class
com\hotel\config\CorsConfig.class
com\hotel\controller\OrderController.class
com\hotel\service\impl\UserServiceImpl.class
com\hotel\service\impl\OrderServiceImpl.class
com\hotel\entity\OrderItem.class
com\hotel\config\MetaObjectHandlerConfig$1.class
com\hotel\config\WebConfig.class
com\hotel\dto\RegisterRequest.class
com\hotel\config\SecurityConfig.class
com\hotel\dto\LoginRequest.class
com\hotel\service\UserService.class
com\hotel\service\impl\AuthServiceImpl.class
com\hotel\security\JwtAuthenticationFilter.class
com\hotel\config\MetaObjectHandlerConfig.class
com\hotel\controller\TestController.class
com\hotel\mapper\OrderItemMapper.class
