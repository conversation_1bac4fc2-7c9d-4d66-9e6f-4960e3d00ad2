<template>
  <div class="user-test-page">
    <el-card>
      <template #header>
        <h3>用户管理功能测试</h3>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- 用户列表 -->
        <el-card>
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>用户列表</span>
              <el-button type="primary" size="small" @click="refreshUsers">刷新</el-button>
            </div>
          </template>
          <p>总数: {{ userStore.total }}</p>
          <p>当前页: {{ userStore.users.length }} 条</p>
          <p>最后更新: {{ lastUserUpdate }}</p>
          
          <el-table :data="userStore.users" style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="role" label="角色" width="100">
              <template #default="{ row }">
                <el-tag :type="row.role === 'ADMIN' ? 'danger' : 'primary'">
                  {{ row.role === 'ADMIN' ? '管理员' : '服务员' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        
        <!-- 测试操作 -->
        <el-divider>测试操作</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="success" @click="testAddUser" :loading="addingUser">
              测试添加用户
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="warning" @click="testUpdateUser" :loading="updatingUser">
              测试更新用户
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="danger" @click="testDeleteUser" :loading="deletingUser">
              测试删除用户
            </el-button>
          </el-col>
        </el-row>
        
        <!-- 操作日志 -->
        <el-divider>最近操作日志</el-divider>
        
        <el-card>
          <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action" :class="log.type">{{ log.action }}</span>
            <span class="log-result" :class="log.success ? 'success' : 'error'">
              {{ log.success ? '成功' : '失败' }}
            </span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserManagementStore } from '@/stores/userManagement'
import { ElMessage } from 'element-plus'

const userStore = useUserManagementStore()

const lastUserUpdate = ref('')
const addingUser = ref(false)
const updatingUser = ref(false)
const deletingUser = ref(false)
const operationLogs = ref([])

const addLog = (action, type, success, message) => {
  operationLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    action,
    type,
    success,
    message
  })
  if (operationLogs.value.length > 10) {
    operationLogs.value.pop()
  }
}

const refreshUsers = async () => {
  try {
    await userStore.fetchUsers({ page: 1, size: 10 })
    lastUserUpdate.value = new Date().toLocaleTimeString()
    addLog('刷新用户数据', 'info', true, `获取到 ${userStore.users.length} 条用户`)
  } catch (error) {
    addLog('刷新用户数据', 'info', false, error.message)
  }
}

const testAddUser = async () => {
  addingUser.value = true
  try {
    const timestamp = Date.now().toString().slice(-6) // 只取最后6位
    const testUser = {
      username: `test${timestamp}`,
      name: `测试用户${timestamp}`,
      role: 'STAFF',
      password: '123456'
    }
    await userStore.addUser(testUser)
    addLog('添加用户', 'success', true, `成功添加用户: ${testUser.name}`)
    ElMessage.success('测试用户添加成功')
    await refreshUsers()
  } catch (error) {
    addLog('添加用户', 'success', false, error.message)
    ElMessage.error('测试用户添加失败: ' + error.message)
  } finally {
    addingUser.value = false
  }
}

const testUpdateUser = async () => {
  if (userStore.users.length === 0) {
    ElMessage.warning('没有用户可以更新，请先添加用户')
    return
  }
  
  updatingUser.value = true
  try {
    const user = userStore.users.find(u => u.role === 'STAFF')
    if (!user) {
      ElMessage.warning('没有找到可以更新的服务员用户')
      return
    }
    
    const updatedUser = {
      name: `${user.name}_已更新_${Date.now()}`
    }
    await userStore.editUser(user.id, updatedUser)
    addLog('更新用户', 'warning', true, `成功更新用户: ${user.name}`)
    ElMessage.success('测试用户更新成功')
    await refreshUsers()
  } catch (error) {
    addLog('更新用户', 'warning', false, error.message)
    ElMessage.error('测试用户更新失败: ' + error.message)
  } finally {
    updatingUser.value = false
  }
}

const testDeleteUser = async () => {
  const testUsers = userStore.users.filter(u => u.username.startsWith('test'))
  if (testUsers.length === 0) {
    ElMessage.warning('没有测试用户可以删除')
    return
  }
  
  deletingUser.value = true
  try {
    const user = testUsers[testUsers.length - 1]
    await userStore.removeUser(user.id)
    addLog('删除用户', 'danger', true, `成功删除用户: ${user.name}`)
    ElMessage.success('测试用户删除成功')
    await refreshUsers()
  } catch (error) {
    addLog('删除用户', 'danger', false, error.message)
    ElMessage.error('测试用户删除失败: ' + error.message)
  } finally {
    deletingUser.value = false
  }
}

onMounted(() => {
  refreshUsers()
})
</script>

<style scoped>
.user-test-page {
  padding: 20px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.log-time {
  color: #909399;
  font-size: 12px;
  min-width: 80px;
}

.log-action {
  font-weight: 500;
  min-width: 80px;
}

.log-action.info { color: #409eff; }
.log-action.success { color: #67c23a; }
.log-action.warning { color: #e6a23c; }
.log-action.danger { color: #f56c6c; }

.log-result {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 40px;
  text-align: center;
}

.log-result.success {
  background: #f0f9ff;
  color: #67c23a;
}

.log-result.error {
  background: #fef0f0;
  color: #f56c6c;
}

.log-message {
  color: #606266;
  flex: 1;
}
</style>
