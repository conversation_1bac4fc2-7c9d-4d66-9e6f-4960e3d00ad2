<template>
  <el-dropdown 
    trigger="click" 
    placement="bottom-end"
    @visible-change="handleVisibleChange"
  >
    <div class="notification-trigger">
      <el-badge :value="notificationStore.unreadCount" :hidden="notificationStore.unreadCount === 0">
        <el-button type="text">
          <el-icon size="20"><Bell /></el-icon>
        </el-button>
      </el-badge>
    </div>
    
    <template #dropdown>
      <div class="notification-dropdown">
        <!-- 头部 -->
        <div class="notification-header">
          <span class="title">通知中心</span>
          <el-button 
            type="text" 
            size="small" 
            @click="markAllAsRead"
            :disabled="notificationStore.unreadCount === 0"
          >
            全部已读
          </el-button>
        </div>
        
        <!-- 通知列表 -->
        <div class="notification-list" v-loading="notificationStore.loading">
          <div 
            v-if="notificationStore.recentNotifications.length === 0" 
            class="empty-state"
          >
            <el-icon size="48" color="#c0c4cc"><Bell /></el-icon>
            <p>暂无通知</p>
          </div>
          
          <div 
            v-else
            v-for="notification in notificationStore.recentNotifications" 
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-icon">
              <el-icon 
                :color="getNotificationColor(notification.type)"
                size="16"
              >
                <component :is="getNotificationIcon(notification.type)" />
              </el-icon>
            </div>
            
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
            </div>
            
            <div v-if="!notification.read" class="unread-dot"></div>
          </div>
        </div>
        
        <!-- 底部 -->
        <div class="notification-footer">
          <el-button type="text" @click="viewAllNotifications">
            查看全部通知
          </el-button>
        </div>
      </div>
    </template>
  </el-dropdown>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '@/stores/notification'
import { ElMessage } from 'element-plus'
import { 
  Bell, 
  Warning, 
  Success, 
  InfoFilled, 
  CircleCheck,
  Document,
  User
} from '@element-plus/icons-vue'

const router = useRouter()
const notificationStore = useNotificationStore()

// 获取通知图标
const getNotificationIcon = (type) => {
  const iconMap = {
    'order': Document,
    'system': InfoFilled,
    'warning': Warning,
    'success': CircleCheck,
    'user': User,
    'default': Bell
  }
  return iconMap[type] || iconMap.default
}

// 获取通知颜色
const getNotificationColor = (type) => {
  const colorMap = {
    'order': '#409eff',
    'system': '#909399',
    'warning': '#e6a23c',
    'success': '#67c23a',
    'user': '#409eff',
    'default': '#909399'
  }
  return colorMap[type] || colorMap.default
}

// 格式化时间
const formatTime = (time) => {
  const now = new Date()
  const notificationTime = new Date(time)
  const diff = now - notificationTime
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return notificationTime.toLocaleDateString()
}

// 处理下拉框显示/隐藏
const handleVisibleChange = (visible) => {
  if (visible) {
    notificationStore.fetchNotifications()
  }
}

// 点击通知项
const handleNotificationClick = async (notification) => {
  try {
    if (!notification.read) {
      await notificationStore.markNotificationAsRead(notification.id)
    }
    
    // 根据通知类型跳转到相应页面
    if (notification.link) {
      router.push(notification.link)
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 标记所有为已读
const markAllAsRead = async () => {
  try {
    await notificationStore.markAllNotificationsAsRead()
    ElMessage.success('已标记所有通知为已读')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 查看全部通知
const viewAllNotifications = () => {
  router.push('/notifications')
}

// 初始化
onMounted(() => {
  notificationStore.fetchNotifications()
})
</script>

<style scoped>
.notification-trigger {
  cursor: pointer;
}

.notification-dropdown {
  width: 320px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-header .title {
  font-weight: 600;
  color: #303133;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 11px;
  color: #909399;
}

.unread-dot {
  width: 6px;
  height: 6px;
  background-color: #f56c6c;
  border-radius: 50%;
  margin-left: 8px;
  margin-top: 6px;
  flex-shrink: 0;
}

.notification-footer {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

.notification-footer .el-button {
  font-size: 12px;
}
</style>
