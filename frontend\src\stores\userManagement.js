import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getUsers, createUser, updateUser, deleteUser, resetUserPassword, toggleUserStatus as toggleUserStatusAPI } from '@/api/user'

export const useUserManagementStore = defineStore('userManagement', () => {
  const users = ref([])
  const total = ref(0)
  const loading = ref(false)

  // 获取用户列表
  const fetchUsers = async (params = {}) => {
    loading.value = true
    try {
      const response = await getUsers(params)
      // 后端返回的是PageResult结构，数据在list字段中
      if (response.data.list) {
        users.value = response.data.list
        total.value = response.data.total || 0
      } else if (response.data.records) {
        users.value = response.data.records
        total.value = response.data.total || 0
      } else if (response.data.data) {
        users.value = response.data.data
        total.value = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        users.value = response.data
        total.value = response.data.length
      } else {
        users.value = []
        total.value = 0
      }

      return response
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 添加用户
  const addUser = async (userData) => {
    try {
      const response = await createUser(userData)
      return response
    } catch (error) {
      console.error('添加用户失败:', error)
      throw error
    }
  }

  // 更新用户
  const editUser = async (id, userData) => {
    try {
      const response = await updateUser(id, userData)
      return response
    } catch (error) {
      console.error('更新用户失败:', error)
      throw error
    }
  }

  // 删除用户
  const removeUser = async (id) => {
    try {
      const response = await deleteUser(id)
      return response
    } catch (error) {
      console.error('删除用户失败:', error)
      throw error
    }
  }

  // 重置用户密码
  const resetPassword = async (id, password) => {
    try {
      const response = await resetUserPassword(id, password)
      return response
    } catch (error) {
      console.error('重置密码失败:', error)
      throw error
    }
  }

  // 切换用户状态
  const toggleUserStatus = async (id) => {
    try {
      const response = await toggleUserStatusAPI(id)
      return response
    } catch (error) {
      console.error('切换用户状态失败:', error)
      throw error
    }
  }

  return {
    users,
    total,
    loading,
    fetchUsers,
    addUser,
    editUser,
    removeUser,
    resetPassword,
    toggleUserStatus
  }
})
