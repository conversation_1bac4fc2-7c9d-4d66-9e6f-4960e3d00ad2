<template>
  <div class="order-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>订单详情</h2>
        <p>查看订单的详细信息</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <div v-loading="loading" v-if="order">
      <el-row :gutter="20">
        <!-- 订单基本信息 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <span>基本信息</span>
                <el-tag :type="getStatusType(order.status)" size="large">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </div>
            </template>

            <div class="info-grid">
              <div class="info-item">
                <label>订单号：</label>
                <span class="order-id">#{{ order.id }}</span>
              </div>
              <div class="info-item">
                <label>桌号：</label>
                <span>{{ order.tableNumber }}</span>
              </div>
              <div class="info-item">
                <label>服务员：</label>
                <span>{{ order.userName || '未知' }}</span>
              </div>
              <div class="info-item">
                <label>订单金额：</label>
                <span class="price">¥{{ order.totalPrice }}</span>
              </div>
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDate(order.createdAt) }}</span>
              </div>
              <div class="info-item">
                <label>更新时间：</label>
                <span>{{ formatDate(order.updatedAt) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 状态操作 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="actions-card">
            <template #header>
              <span>订单操作</span>
            </template>

            <div class="status-timeline">
              <el-steps :active="getStatusStep(order.status)" direction="vertical" finish-status="success">
                <el-step title="待处理" description="订单已创建，等待处理" />
                <el-step title="准备中" description="厨房正在准备菜品" />
                <el-step title="已完成" description="菜品已完成，可以上菜" />
              </el-steps>
            </div>

            <div class="action-buttons">
              <el-button
                v-if="order.status === 'PENDING'"
                type="primary"
                @click="updateStatus('PREPARING')"
              >
                开始准备
              </el-button>
              <el-button
                v-if="order.status === 'PREPARING'"
                type="success"
                @click="updateStatus('COMPLETED')"
              >
                完成订单
              </el-button>
              <el-button
                v-if="order.status !== 'CANCELLED' && order.status !== 'COMPLETED'"
                type="danger"
                @click="updateStatus('CANCELLED')"
              >
                取消订单
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 订单明细 -->
      <el-card class="items-card">
        <template #header>
          <span>订单明细</span>
        </template>

        <el-table :data="order.items || []" style="width: 100%">
          <el-table-column prop="dishName" label="菜品名称" />
          <el-table-column prop="price" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="80" />
          <el-table-column label="小计" width="100">
            <template #default="{ row }">
              ¥{{ (row.price * row.quantity).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" />
        </el-table>

        <div class="order-summary">
          <div class="summary-row">
            <span>商品总数：</span>
            <span>{{ totalQuantity }} 份</span>
          </div>
          <div class="summary-row total">
            <span>订单总额：</span>
            <span class="total-price">¥{{ order.totalPrice }}</span>
          </div>
        </div>
      </el-card>

      <!-- 操作日志 -->
      <el-card class="logs-card">
        <template #header>
          <span>操作日志</span>
        </template>

        <el-timeline>
          <el-timeline-item
            v-for="log in orderLogs"
            :key="log.id"
            :timestamp="log.timestamp"
            :type="log.type"
          >
            {{ log.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrderStore } from '@/stores/order'
import { ElMessage, ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const orderStore = useOrderStore()

const loading = ref(false)

const order = ref(null)

const orderLogs = ref([
  {
    id: 1,
    content: '订单状态更新为：准备中',
    timestamp: '2023-07-15 14:35:00',
    type: 'primary'
  },
  {
    id: 2,
    content: '订单已创建',
    timestamp: '2023-07-15 14:30:00',
    type: 'success'
  }
])

const totalQuantity = computed(() => {
  if (!order.value || !order.value.items) return 0
  return order.value.items.reduce((total, item) => total + item.quantity, 0)
})

const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'PREPARING': 'info',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待处理',
    'PREPARING': '准备中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const getStatusStep = (status) => {
  const stepMap = {
    'PENDING': 0,
    'PREPARING': 1,
    'COMPLETED': 2,
    'CANCELLED': -1
  }
  return stepMap[status] || 0
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const fetchOrderDetail = async () => {
  const orderId = route.params.id
  if (!orderId) return

  loading.value = true
  try {
    const response = await orderStore.fetchOrder(orderId)
    order.value = response.data
  } catch (error) {
    ElMessage.error('获取订单详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const updateStatus = async (newStatus) => {
  try {
    const statusText = getStatusText(newStatus)
    await ElMessageBox.confirm(
      `确定要将订单状态更新为"${statusText}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    order.status = newStatus
    order.updatedAt = new Date().toLocaleString('zh-CN')
    
    // 添加操作日志
    orderLogs.value.unshift({
      id: Date.now(),
      content: `订单状态更新为：${statusText}`,
      timestamp: order.updatedAt,
      type: getStatusType(newStatus)
    })

    ElMessage.success('状态更新成功')
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.order-detail-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.info-card,
.actions-card,
.items-card,
.logs-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.order-id {
  color: #409eff;
  font-weight: 600;
}

.price,
.total-price {
  color: #409eff;
  font-weight: bold;
}

.status-timeline {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.order-summary {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  text-align: right;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-row.total {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-item label {
    margin-bottom: 4px;
    min-width: auto;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
