<template>
  <div class="dishes-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>菜品管理</h2>
        <p>管理餐厅所有菜品信息</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/dishes/add')">
          <el-icon><Plus /></el-icon>
          添加菜品
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="菜品名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入菜品名称"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable style="width: 120px">
            <el-option label="开胃菜" value="APPETIZER" />
            <el-option label="主菜" value="MAIN" />
            <el-option label="甜点" value="DESSERT" />
            <el-option label="饮品" value="DRINK" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="上架中" :value="1" />
            <el-option label="已下架" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 菜品列表 -->
    <el-card class="dishes-card">
      <div class="dishes-grid">
        <div
          v-for="dish in dishes"
          :key="dish.id"
          class="dish-item"
        >
          <div class="dish-image">
            <img :src="dish.imageUrl || '/placeholder-dish.jpg'" :alt="dish.name" />
            <div class="dish-status">
              <el-tag :type="dish.status ? 'success' : 'danger'">
                {{ dish.status ? '上架中' : '已下架' }}
              </el-tag>
            </div>
          </div>
          
          <div class="dish-content">
            <h3 class="dish-name">{{ dish.name }}</h3>
            <p class="dish-description">{{ dish.description }}</p>
            
            <div class="dish-meta">
              <span class="dish-category">
                <el-tag :type="getCategoryType(dish.category)" size="small">
                  {{ getCategoryText(dish.category) }}
                </el-tag>
              </span>
              <span class="dish-price">¥{{ dish.price }}</span>
            </div>
            
            <div class="dish-actions">
              <el-button type="text" @click="editDish(dish.id)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                type="text" 
                :class="dish.status ? 'danger' : 'success'"
                @click="toggleDishStatus(dish)"
              >
                <el-icon><Switch /></el-icon>
                {{ dish.status ? '下架' : '上架' }}
              </el-button>
              <el-button type="text" class="danger" @click="deleteDish(dish)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[12, 24, 48, 96]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDishStore } from '@/stores/dish'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const dishStore = useDishStore()

const searchForm = reactive({
  name: '',
  category: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 12,
  total: 0
})

const dishes = ref([])

const getCategoryType = (category) => {
  const typeMap = {
    'APPETIZER': 'danger',
    'MAIN': 'success',
    'DESSERT': 'warning',
    'DRINK': 'info'
  }
  return typeMap[category] || 'info'
}

const getCategoryText = (category) => {
  const textMap = {
    'APPETIZER': '开胃菜',
    'MAIN': '主菜',
    'DESSERT': '甜点',
    'DRINK': '饮品'
  }
  return textMap[category] || category
}

const handleSearch = () => {
  pagination.page = 1
  fetchDishes()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    category: '',
    status: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchDishes()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchDishes()
}

const fetchDishes = async () => {
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    await dishStore.fetchDishes(params)
    dishes.value = dishStore.dishes
    pagination.total = dishStore.total
  } catch (error) {
    ElMessage.error('获取菜品列表失败')
  }
}

const editDish = (dishId) => {
  router.push(`/dishes/edit/${dishId}`)
}

const toggleDishStatus = async (dish) => {
  try {
    const action = dish.status ? '下架' : '上架'
    await ElMessageBox.confirm(`确定要${action}菜品"${dish.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API更新菜品状态
    const newStatus = dish.status ? 0 : 1
    await dishStore.editDish(dish.id, { ...dish, status: newStatus })
    dish.status = newStatus
    ElMessage.success(`${action}成功`)
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

const deleteDish = async (dish) => {
  try {
    await ElMessageBox.confirm(`确定要删除菜品"${dish.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API删除菜品
    await dishStore.removeDish(dish.id)
    ElMessage.success('删除成功')
    // 重新获取菜品列表
    await fetchDishes()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

onMounted(() => {
  fetchDishes()
})
</script>

<style scoped>
.dishes-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dishes-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dishes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.dish-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  background: white;
}

.dish-item:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.dish-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.dish-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dish-status {
  position: absolute;
  top: 8px;
  right: 8px;
}

.dish-content {
  padding: 16px;
}

.dish-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.dish-description {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dish-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dish-price {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
}

.dish-actions {
  display: flex;
  gap: 8px;
}

.dish-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.dish-actions .danger {
  color: #f56c6c;
}

.dish-actions .success {
  color: #67c23a;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .dishes-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-card .el-form {
    flex-direction: column;
  }
  
  .filter-card .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
